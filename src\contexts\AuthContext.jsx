import React, { createContext, useState, useContext } from 'react';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState({ role: '' });

  // هنا يمكنك تخزين معلومات المستخدم بعد التحقق من تسجيل الدخول
  const login = (role) => {
    setUser({ role });
  };

  return (
    <AuthContext.Provider value={{ user, login }}>
      {children}
    </AuthContext.Provider>
  );
};
