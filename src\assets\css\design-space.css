/* Canva-style Design Space CSS */

/* Base styles */
:root {
  --canva-primary: #8b3dff;
  --canva-primary-hover: #7a2bff;
  --canva-secondary: #00c4cc;
  --canva-text: #333333;
  --canva-light-gray: #f5f5f5;
  --canva-gray: #e6e6e6;
  --canva-dark-gray: #666666;
  --canva-border: #dddddd;
  --canva-white: #ffffff;
  --canva-black: #000000;
  --canva-success: #00c48c;
  --canva-warning: #ffb237;
  --canva-danger: #ff5c5c;
}

body {
  overflow-x: hidden;
  font-family: 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--canva-text);
}

/* Toolbar styles */
.canva-toolbar {
  background-color: var(--canva-white);
  border-bottom: 1px solid var(--canva-border);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  height: 56px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.canva-toolbar button {
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--canva-text);
  transition: all 0.2s ease;
}

.canva-toolbar button:hover {
  background-color: var(--canva-light-gray);
}

.canva-toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Side menu styles */
.canva-side-menu {
  background-color: var(--canva-white);
  border-right: 1px solid var(--canva-border);
  height: 100%;
  width: 100%;
}

.canva-side-menu-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--canva-border);
}

.canva-side-menu-tabs {
  display: flex;
  overflow-x: auto;
  scrollbar-width: thin;
  border-bottom: 1px solid var(--canva-border);
}

.canva-side-menu-tabs::-webkit-scrollbar {
  height: 4px;
}

.canva-side-menu-tabs::-webkit-scrollbar-thumb {
  background-color: var(--canva-dark-gray);
  border-radius: 4px;
}

.canva-side-menu-content {
  padding: 16px;
  overflow-y: auto;
}

/* Element styles */
.resize-handle {
  background-color: #8b3dff;
  position: absolute;
  width: 12px;
  height: 12px;
  cursor: se-resize;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0.9;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.2s ease;
}

.resize-handle:hover {
  transform: scale(1.3);
  background-color: #00c4cc;
  opacity: 1;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(0, 196, 204, 0.4);
}

.resize-handle:hover::before {
  transform: translate(-50%, -50%) scale(1);
}

/* Rotation handle */
.rotation-handle {
  position: absolute;
  width: 14px;
  height: 14px;
  background-color: #00c4cc;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2);
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  cursor: grab;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0;
}

.rotation-handle::before {
  content: '';
  position: absolute;
  top: 14px;
  left: 50%;
  height: 10px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.3);
  transform: translateX(-50%);
}

.rotation-handle::after {
  content: '↻';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: white;
}

.selected .rotation-handle {
  opacity: 1;
}

.rotation-handle:hover {
  transform: translateX(-50%) scale(1.2);
  background-color: #00d8e0;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(0, 196, 204, 0.4);
}

/* Skew handles */
.skew-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #ff9500;
  border-radius: 2px;
  border: 2px solid white;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0;
  transform: rotate(45deg);
}

.skew-handle.top {
  top: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  cursor: ns-resize;
}

.skew-handle.right {
  top: 50%;
  right: -5px;
  transform: translateY(-50%) rotate(45deg);
  cursor: ew-resize;
}

.skew-handle.bottom {
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  cursor: ns-resize;
}

.skew-handle.left {
  top: 50%;
  left: -5px;
  transform: translateY(-50%) rotate(45deg);
  cursor: ew-resize;
}

.selected .skew-handle {
  opacity: 1;
}

.skew-handle:hover {
  transform: translateX(-50%) scale(1.3) rotate(45deg);
  background-color: #ffb340;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(255, 149, 0, 0.4);
}

.skew-handle.right:hover, .skew-handle.left:hover {
  transform: translateY(-50%) scale(1.3) rotate(45deg);
}

.draggable-element {
  position: absolute;
  background-color: transparent;
  border: 1px solid transparent;
  cursor: grab;
  transition: all 0.2s ease;
}

.draggable-element:hover {
  border: 1px dashed var(--canva-dark-gray);
}

/* Element controls that appear on selection */
.element-controls {
  position: absolute;
  display: none;
  top: -55px;
  right: -10px;
  background: #2c3e50;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  padding: 6px;
  z-index: 100;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  transform: scale(0.95) translateY(5px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.element-controls::after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 15px;
  width: 12px;
  height: 12px;
  background: #2c3e50;
  transform: rotate(45deg);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.15);
  z-index: -1;
}

.selected .element-controls {
  display: flex;
  transform: scale(1) translateY(0);
  opacity: 1;
}

.element-control-btn {
  width: 34px;
  height: 34px;
  border: none;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
  position: relative;
  margin: 0 1px;
}

.element-control-btn svg {
  width: 18px;
  height: 18px;
  transition: all 0.2s ease;
}

.element-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  transform: translateY(-2px);
}

.element-control-btn:active {
  transform: translateY(0);
  background-color: rgba(255, 255, 255, 0.2);
}

/* Rotate button */
.element-control-btn.rotate-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.rotate-btn:hover {
  background-color: rgba(138, 61, 255, 0.7);
  color: white;
}

/* Forward button */
.element-control-btn.forward-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.forward-btn:hover {
  background-color: rgba(0, 196, 204, 0.7);
  color: white;
}

/* Backward button */
.element-control-btn.backward-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.backward-btn:hover {
  background-color: rgba(0, 196, 204, 0.7);
  color: white;
}

/* Delete button */
.element-control-btn.delete-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.delete-btn:hover {
  background-color: rgba(255, 92, 92, 0.7);
  color: white;
}

/* Duplicate button */
.element-control-btn.duplicate-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.duplicate-btn:hover {
  background-color: rgba(0, 196, 140, 0.7);
  color: white;
}

/* Tooltip for element controls */
.element-control-btn::after {
  content: attr(title);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%) scale(0);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none;
  z-index: 101;
}

.element-control-btn:hover::after {
  transform: translateX(-50%) scale(1);
  opacity: 1;
}

/* Layer indicator */
.layer-indicator {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--canva-primary);
  color: var(--canva-white);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.selected .layer-indicator {
  opacity: 1;
}

.design-space {
  position: relative;
  overflow: hidden;
  background-color: #f8f9fa;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.5) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.2),
    0 0 8px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  transform-style: preserve-3d;
  perspective: 1200px;
  transform: rotateX(2deg);
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.design-space::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 50%),
    linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0.05));
  pointer-events: none;
  z-index: 1;
  border-radius: 8px;
}

.design-space::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 5%;
  right: 5%;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.15);
  filter: blur(8px);
  border-radius: 50%;
  z-index: -1;
  transform-origin: center;
  animation: shadow-pulse 5s ease-in-out infinite alternate;
}

@keyframes shadow-pulse {
  0% {
    transform: scaleX(0.95);
    opacity: 0.15;
  }
  100% {
    transform: scaleX(1);
    opacity: 0.2;
  }
}

.design-space > div {
  position: absolute;
}

.design-space:hover {
  transform: rotateX(0deg) translateY(-5px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.25),
    0 0 15px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.6);
}

/* Professional corner marks */
.design-space::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  pointer-events: none;
  z-index: 2;
}

.design-space::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 5%;
  right: 5%;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.15);
  filter: blur(8px);
  border-radius: 50%;
  z-index: -1;
}

/* Corner marks */
.design-space .corner-mark {
  position: absolute;
  width: 15px;
  height: 15px;
  border: 2px solid rgba(0, 0, 0, 0.2);
  pointer-events: none;
  z-index: 2;
}

.design-space .corner-mark.top-left {
  top: 5px;
  left: 5px;
  border-right: none;
  border-bottom: none;
}

.design-space .corner-mark.top-right {
  top: 5px;
  right: 5px;
  border-left: none;
  border-bottom: none;
}

.design-space .corner-mark.bottom-left {
  bottom: 5px;
  left: 5px;
  border-right: none;
  border-top: none;
}

.design-space .corner-mark.bottom-right {
  bottom: 5px;
  right: 5px;
  border-left: none;
  border-top: none;
}

.selected {
  border: 2px solid var(--canva-primary) !important;
  box-shadow: 0 0 0 1px var(--canva-primary), 0 0 8px rgba(139, 61, 255, 0.3);
}

/* Alignment controls */
.alignment-container button {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--canva-border);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--canva-white);
  color: var(--canva-text);
  padding: 8px;
  border-radius: 4px;
}

.alignment-container button:hover {
  background-color: var(--canva-light-gray);
}

.alignment-container button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Element buttons */
.elements-btns-container button {
  background-color: var(--canva-primary);
  border-radius: 4px;
  transition: all 0.2s ease;
  padding: 8px 16px;
  width: 100%;
  color: var(--canva-white);
  border: none;
  font-weight: 500;
  cursor: pointer;
}

.elements-btns-container button:hover {
  background-color: var(--canva-primary-hover);
}

/* Add element button */
.add-element-btn {
  padding: 16px;
  border: 2px dashed var(--canva-dark-gray);
  color: var(--canva-dark-gray);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  border-radius: 4px;
  cursor: pointer;
}

.add-element-btn:hover {
  color: var(--canva-text);
  background-color: var(--canva-light-gray);
  border-color: var(--canva-text);
}

/* Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.3;
  }
}

/* Table styles */
.sticky-header {
  position: sticky;
  top: 0;
  background-color: var(--canva-white);
  z-index: 50;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.sticky-header thead th {
  position: sticky;
  top: 0;
  background: var(--canva-white);
  z-index: 10;
  box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1);
}

.table-responsive {
  overflow-y: auto;
  height: calc(100vh - 300px);
}

/* Canva-specific components */
.canva-templates h3,
.canva-elements h3,
.canva-photos h3,
.canva-videos h3,
.canva-uploads h3,
.canva-backgrounds h3,
.canva-charts h3,
.canva-tables h3 {
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--canva-text);
}

/* Search inputs */
.canva-side-menu input[type="text"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--canva-border);
  border-radius: 4px;
  font-size: 14px;
  transition: border 0.2s ease;
}

.canva-side-menu input[type="text"]:focus {
  border-color: var(--canva-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(139, 61, 255, 0.2);
}

/* Dropdown menus */
.canva-toolbar .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--canva-white);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  min-width: 180px;
  padding: 8px 0;
}

.canva-toolbar .dropdown-menu li {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.canva-toolbar .dropdown-menu li:hover {
  background-color: var(--canva-light-gray);
}

/* Zoom controls */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-percentage {
  font-size: 14px;
  font-weight: 500;
  min-width: 48px;
  text-align: center;
}

/* Layer controls */
.layer-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Group controls */
.group-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}