import React from 'react'
import SideMenu from './SideMenu'
import Banner from './Banner'

function Layout({ children }) {
    return (
        <main className='flex h-[100vh] bg-[#f6f6f6]'>
            <aside className='w-2/12 '>
                <SideMenu />
            </aside>
            <section className='w-10/12 flex flex-col '>
                {/* <Banner />                                      Disabled Navbar */}

                <div className="w-full p-5 h-[95vh] overflow-y-auto">
                    {children}
                </div>
            </section>
        </main>
    )
}

export default Layout