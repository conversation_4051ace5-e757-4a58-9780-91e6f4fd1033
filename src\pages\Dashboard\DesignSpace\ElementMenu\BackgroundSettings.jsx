import React, { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { IoColorPaletteOutline } from 'react-icons/io5';
import { MdOutlineTexture, MdOutlineGradient, MdOutlineImage, MdOutlinePattern } from 'react-icons/md';
import { HiOutlinePhotograph } from 'react-icons/hi';
import { v4 as uuidv4 } from 'uuid';

// Background categories and items
const backgroundCategories = [
  {
    id: 'solid-colors',
    name: 'Solid Colors',
    icon: <IoColorPaletteOutline size={20} />,
    items: [
      { id: 'color-red', name: 'Red', color: '#FF5733', type: 'solid' },
      { id: 'color-orange', name: 'Orange', color: '#FFA533', type: 'solid' },
      { id: 'color-yellow', name: 'Yellow', color: '#FFE033', type: 'solid' },
      { id: 'color-green', name: 'Green', color: '#33FF57', type: 'solid' },
      { id: 'color-teal', name: 'Teal', color: '#33FFC4', type: 'solid' },
      { id: 'color-blue', name: 'Blue', color: '#3357FF', type: 'solid' },
      { id: 'color-purple', name: 'Purple', color: '#8333FF', type: 'solid' },
      { id: 'color-pink', name: 'Pink', color: '#FF33F3', type: 'solid' },
      { id: 'color-black', name: 'Black', color: '#000000', type: 'solid' },
      { id: 'color-gray', name: 'Gray', color: '#808080', type: 'solid' },
      { id: 'color-white', name: 'White', color: '#FFFFFF', type: 'solid' },
      { id: 'color-brown', name: 'Brown', color: '#8B4513', type: 'solid' },
    ]
  },
  {
    id: 'gradients',
    name: 'Gradients',
    icon: <MdOutlineGradient size={20} />,
    items: [
      { id: 'gradient-sunset', name: 'Sunset', color: 'linear-gradient(45deg, #FF5733, #FFA533)', type: 'gradient' },
      { id: 'gradient-ocean', name: 'Ocean', color: 'linear-gradient(45deg, #3357FF, #33FFC4)', type: 'gradient' },
      { id: 'gradient-forest', name: 'Forest', color: 'linear-gradient(45deg, #33FF57, #33FFC4)', type: 'gradient' },
      { id: 'gradient-berry', name: 'Berry', color: 'linear-gradient(45deg, #8333FF, #FF33F3)', type: 'gradient' },
      { id: 'gradient-fire', name: 'Fire', color: 'linear-gradient(45deg, #FF5733, #FF3333)', type: 'gradient' },
      { id: 'gradient-sky', name: 'Sky', color: 'linear-gradient(45deg, #33C4FF, #3357FF)', type: 'gradient' },
      { id: 'gradient-mint', name: 'Mint', color: 'linear-gradient(45deg, #33FF57, #33FFC4)', type: 'gradient' },
      { id: 'gradient-peach', name: 'Peach', color: 'linear-gradient(45deg, #FFA533, #FF5733)', type: 'gradient' },
      { id: 'gradient-lavender', name: 'Lavender', color: 'linear-gradient(45deg, #8333FF, #C433FF)', type: 'gradient' },
      { id: 'gradient-midnight', name: 'Midnight', color: 'linear-gradient(45deg, #000033, #3357FF)', type: 'gradient' },
      { id: 'gradient-sunrise', name: 'Sunrise', color: 'linear-gradient(45deg, #FFA533, #FFE033)', type: 'gradient' },
      { id: 'gradient-twilight', name: 'Twilight', color: 'linear-gradient(45deg, #3357FF, #8333FF)', type: 'gradient' },
    ]
  },
  {
    id: 'patterns',
    name: 'Patterns',
    icon: <MdOutlinePattern size={20} />,
    items: [
      { id: 'pattern-dots', name: 'Dots', color: 'radial-gradient(circle, #4338ca 2px, transparent 2px)', type: 'pattern', style: { backgroundSize: '15px 15px' } },
      { id: 'pattern-stripes', name: 'Stripes', color: 'repeating-linear-gradient(45deg, #4338ca, #4338ca 5px, transparent 5px, transparent 25px)', type: 'pattern' },
      { id: 'pattern-zigzag', name: 'Zigzag', color: 'linear-gradient(135deg, #4338ca 25%, transparent 25%) 0 0, linear-gradient(225deg, #4338ca 25%, transparent 25%) 0 0, linear-gradient(315deg, #4338ca 25%, transparent 25%) 0 0, linear-gradient(45deg, #4338ca 25%, transparent 25%) 0 0', type: 'pattern', style: { backgroundSize: '20px 20px' } },
      { id: 'pattern-grid', name: 'Grid', color: 'linear-gradient(to right, #4338ca 1px, transparent 1px), linear-gradient(to bottom, #4338ca 1px, transparent 1px)', type: 'pattern', style: { backgroundSize: '20px 20px' } },
      { id: 'pattern-triangles', name: 'Triangles', color: 'linear-gradient(60deg, #4338ca 25%, transparent 25.5%) 0 0, linear-gradient(120deg, #4338ca 25%, transparent 25.5%) 0 0, linear-gradient(60deg, transparent 75%, #4338ca 75.5%) 0 0, linear-gradient(120deg, transparent 75%, #4338ca 75.5%) 0 0', type: 'pattern', style: { backgroundSize: '30px 50px' } },
      { id: 'pattern-waves', name: 'Waves', color: 'linear-gradient(to right, #4338ca, #4338ca 5px, transparent 5px, transparent 10px)', type: 'pattern', style: { backgroundSize: '20px 100%' } },
      { id: 'pattern-confetti', name: 'Confetti', color: 'radial-gradient(circle, #FF5733 4px, transparent 4px) 0 0, radial-gradient(circle, #33FF57 4px, transparent 4px) 10px 10px, radial-gradient(circle, #3357FF 4px, transparent 4px) 20px 20px', type: 'pattern', style: { backgroundSize: '30px 30px' } },
      { id: 'pattern-geometric', name: 'Geometric', color: 'linear-gradient(30deg, #4338ca 12%, transparent 12.5%, transparent 87%, #4338ca 87.5%, #4338ca) 0 0, linear-gradient(150deg, #4338ca 12%, transparent 12.5%, transparent 87%, #4338ca 87.5%, #4338ca) 0 0, linear-gradient(30deg, #4338ca 12%, transparent 12.5%, transparent 87%, #4338ca 87.5%, #4338ca) 0 0, linear-gradient(150deg, #4338ca 12%, transparent 12.5%, transparent 87%, #4338ca 87.5%, #4338ca) 0 0', type: 'pattern', style: { backgroundSize: '40px 70px' } },
    ]
  },
  {
    id: 'textures',
    name: 'Textures',
    icon: <MdOutlineTexture size={20} />,
    items: [
      { id: 'texture-paper', name: 'Paper', color: 'linear-gradient(135deg, #f5f5f5 22px, #e0e0e0 22px, #e0e0e0 24px, transparent 24px, transparent 67px, #e0e0e0 67px, #e0e0e0 69px, transparent 69px), linear-gradient(225deg, #f5f5f5 22px, #e0e0e0 22px, #e0e0e0 24px, transparent 24px, transparent 67px, #e0e0e0 67px, #e0e0e0 69px, transparent 69px) 0 64px', type: 'texture', style: { backgroundSize: '64px 128px', backgroundColor: '#f5f5f5' } },
      { id: 'texture-marble', name: 'Marble', color: 'radial-gradient(circle at 50% 50%, rgba(200, 200, 200, 0.8) 0%, rgba(140, 140, 140, 0.6) 40%, rgba(100, 100, 100, 0.4) 80%, rgba(240, 240, 240, 0.9) 100%), radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.8) 0%, rgba(220, 220, 220, 0.6) 40%, rgba(200, 200, 200, 0.4) 80%, rgba(240, 240, 240, 0.9) 100%)', type: 'texture', style: { backgroundBlendMode: 'multiply' } },
      { id: 'texture-wood', name: 'Wood', color: 'repeating-linear-gradient(90deg, rgba(166, 128, 94, 0.5) 0px, rgba(166, 128, 94, 0.5) 2px, rgba(140, 103, 70, 0.5) 2px, rgba(140, 103, 70, 0.5) 4px), repeating-linear-gradient(0deg, rgba(166, 128, 94, 0.4) 0px, rgba(166, 128, 94, 0.4) 1px, rgba(120, 88, 60, 0.4) 1px, rgba(120, 88, 60, 0.4) 3px)', type: 'texture', style: { backgroundColor: '#a6805e' } },
      { id: 'texture-concrete', name: 'Concrete', color: 'linear-gradient(90deg, rgba(200, 200, 200, 0.5) 50%, transparent 50%), linear-gradient(rgba(200, 200, 200, 0.5) 50%, transparent 50%)', type: 'texture', style: { backgroundSize: '4px 4px', backgroundColor: '#d9d9d9' } },
      { id: 'texture-fabric', name: 'Fabric', color: 'radial-gradient(circle, transparent 20%, rgba(220, 220, 220, 0.8) 20%, rgba(220, 220, 220, 0.8) 80%, transparent 80%, transparent), radial-gradient(circle, transparent 20%, rgba(220, 220, 220, 0.8) 20%, rgba(220, 220, 220, 0.8) 80%, transparent 80%, transparent) 25px 25px', type: 'texture', style: { backgroundSize: '50px 50px', backgroundColor: '#f0f0f0' } },
      { id: 'texture-leather', name: 'Leather', color: 'linear-gradient(45deg, rgba(100, 70, 50, 0.4) 25%, transparent 25%, transparent 75%, rgba(100, 70, 50, 0.4) 75%, rgba(100, 70, 50, 0.4)), linear-gradient(-45deg, rgba(100, 70, 50, 0.4) 25%, transparent 25%, transparent 75%, rgba(100, 70, 50, 0.4) 75%, rgba(100, 70, 50, 0.4))', type: 'texture', style: { backgroundSize: '10px 10px', backgroundColor: '#8B4513' } },
      { id: 'texture-metal', name: 'Metal', color: 'linear-gradient(45deg, rgba(200, 200, 200, 0.5) 25%, transparent 25%, transparent 75%, rgba(200, 200, 200, 0.5) 75%, rgba(200, 200, 200, 0.5)), linear-gradient(-45deg, rgba(200, 200, 200, 0.5) 25%, transparent 25%, transparent 75%, rgba(200, 200, 200, 0.5) 75%, rgba(200, 200, 200, 0.5))', type: 'texture', style: { backgroundSize: '10px 10px', backgroundColor: '#a0a0a0' } },
      { id: 'texture-stone', name: 'Stone', color: 'radial-gradient(circle at 50% 50%, rgba(150, 150, 150, 0.8) 0%, rgba(120, 120, 120, 0.6) 40%, rgba(100, 100, 100, 0.4) 80%, rgba(140, 140, 140, 0.9) 100%), radial-gradient(circle at 80% 20%, rgba(160, 160, 160, 0.8) 0%, rgba(130, 130, 130, 0.6) 40%, rgba(110, 110, 110, 0.4) 80%, rgba(150, 150, 150, 0.9) 100%)', type: 'texture', style: { backgroundBlendMode: 'multiply', backgroundColor: '#808080' } },
    ]
  },
  {
    id: 'photos',
    name: 'Photos',
    icon: <HiOutlinePhotograph size={20} />,
    items: [
      { id: 'photo-nature', name: 'Nature', color: 'linear-gradient(to bottom, rgba(0, 100, 0, 0.7), rgba(0, 50, 0, 0.9)), linear-gradient(45deg, #2ecc71 25%, transparent 25%, transparent 75%, #2ecc71 75%, #2ecc71), linear-gradient(-45deg, #27ae60 25%, transparent 25%, transparent 75%, #27ae60 75%, #27ae60)', type: 'photo', style: { backgroundSize: '100px 100px, 60px 60px, 60px 60px', backgroundBlendMode: 'overlay' } },
      { id: 'photo-city', name: 'City', color: 'linear-gradient(to bottom, rgba(50, 50, 100, 0.7), rgba(20, 20, 40, 0.9)), repeating-linear-gradient(0deg, rgba(100, 100, 150, 0.5) 0px, rgba(100, 100, 150, 0.5) 2px, rgba(80, 80, 120, 0.5) 2px, rgba(80, 80, 120, 0.5) 4px)', type: 'photo', style: { backgroundBlendMode: 'overlay' } },
      { id: 'photo-abstract', name: 'Abstract', color: 'linear-gradient(45deg, #ff9a9e 0%, #fad0c4 99%, #fad0c4 100%), radial-gradient(circle at 50% 50%, #fad0c4 0%, #ff9a9e 100%)', type: 'photo', style: { backgroundBlendMode: 'screen' } },
      { id: 'photo-beach', name: 'Beach', color: 'linear-gradient(to bottom, #4facfe 0%, #00f2fe 100%), linear-gradient(45deg, rgba(255, 255, 255, 0.3) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3))', type: 'photo', style: { backgroundSize: '100% 100%, 20px 20px', backgroundBlendMode: 'overlay' } },
      { id: 'photo-mountains', name: 'Mountains', color: 'linear-gradient(to bottom, #5ee7df 0%, #b490ca 100%), linear-gradient(135deg, rgba(100, 100, 100, 0.3) 25%, transparent 25%, transparent 75%, rgba(100, 100, 100, 0.3) 75%, rgba(100, 100, 100, 0.3))', type: 'photo', style: { backgroundSize: '100% 100%, 40px 40px', backgroundBlendMode: 'overlay' } },
      { id: 'photo-sky', name: 'Sky', color: 'linear-gradient(to bottom, #48c6ef 0%, #6f86d6 100%), radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.5) 0%, rgba(200, 200, 255, 0.2) 100%)', type: 'photo', style: { backgroundBlendMode: 'overlay' } },
      { id: 'photo-forest', name: 'Forest', color: 'linear-gradient(to bottom, #0ba360 0%, #3cba92 100%), linear-gradient(45deg, rgba(0, 50, 0, 0.3) 25%, transparent 25%, transparent 75%, rgba(0, 50, 0, 0.3) 75%, rgba(0, 50, 0, 0.3))', type: 'photo', style: { backgroundSize: '100% 100%, 30px 30px', backgroundBlendMode: 'overlay' } },
      { id: 'photo-space', name: 'Space', color: 'linear-gradient(to bottom, #000428 0%, #004e92 100%), radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.8) 0%, rgba(100, 100, 200, 0.1) 100%)', type: 'photo', style: { backgroundBlendMode: 'overlay' } },
    ]
  }
];

function BackgroundSettings() {
  const { setCanvasBackgroundWithStyle } = useDesignSpace();
  const [activeCategory, setActiveCategory] = useState('solid-colors');
  const [searchTerm, setSearchTerm] = useState('');

  // Get the active category data
  const activeCategoryData = backgroundCategories.find(cat => cat.id === activeCategory);

  // Filter items based on search term
  const filteredItems = activeCategoryData?.items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle applying a background
  const handleApplyBackground = (item) => {
    try {
      console.log("Applying background:", item);

      // First, update the context with the background information
      if (item.type === 'solid') {
        // IMPORTANT: For solid colors, we'll use the direct color value
        // This ensures the color is saved correctly in the database
        setCanvasBackgroundWithStyle(item.color, null);
        console.log("Applied solid color to context:", item.color);

        // Also log to verify what's being stored in context
        setTimeout(() => {
          const context = document.querySelector('.design-space');
          if (context) {
            console.log("Context background after applying solid color:", {
              computedBackground: window.getComputedStyle(context).background,
              computedBackgroundColor: window.getComputedStyle(context).backgroundColor,
              computedBackgroundImage: window.getComputedStyle(context).backgroundImage
            });
          }
        }, 100);
      } else if (item.type === 'gradient') {
        setCanvasBackgroundWithStyle(item.color, null);
        console.log("Applied gradient to context:", item.color);
      } else if (item.type === 'pattern' || item.type === 'texture' || item.type === 'photo') {
        const style = item.style || {};
        setCanvasBackgroundWithStyle(item.color, style);
        console.log("Applied pattern/texture/photo to context:", item.color, style);
      }

      // DIRECT DOM MANIPULATION - SUPER SIMPLE APPROACH
      // Get the design space content div directly by ID
      const targetDiv = document.getElementById('design-space-content');

      if (targetDiv) {
        console.log("Found design space content by ID:", targetDiv);

        // For solid colors, apply directly as background-color
        if (item.type === 'solid') {
          // Apply directly as background-color for solid colors
          targetDiv.style.setProperty('background-color', item.color, 'important');
          targetDiv.style.setProperty('background-image', 'none', 'important');

          // Log the current style
          console.log("Current style before direct attribute setting:", targetDiv.getAttribute('style'));

          // Also try setting the attribute directly
          targetDiv.setAttribute('style', targetDiv.getAttribute('style') +
            `; background-color: ${item.color} !important; background-image: none !important;`);

          // Log the style after setting
          console.log("Style after direct attribute setting:", targetDiv.getAttribute('style'));
          console.log("Applied solid color directly:", item.color);
        }
        // For gradients
        else if (item.type === 'gradient') {
          targetDiv.style.setProperty('background-image', item.color, 'important');

          // Also try setting the attribute directly
          targetDiv.setAttribute('style', targetDiv.getAttribute('style') +
            `; background-image: ${item.color} !important;`);

          console.log("Applied gradient directly:", item.color);
        }
        // For patterns, textures, and photos
        else if (item.type === 'pattern' || item.type === 'texture' || item.type === 'photo') {
          const style = item.style || {};
          targetDiv.style.setProperty('background-image', item.color, 'important');

          if (style.backgroundSize)
            targetDiv.style.setProperty('background-size', style.backgroundSize, 'important');
          if (style.backgroundPosition)
            targetDiv.style.setProperty('background-position', style.backgroundPosition, 'important');
          if (style.backgroundRepeat)
            targetDiv.style.setProperty('background-repeat', style.backgroundRepeat, 'important');

          console.log("Applied pattern/texture/photo directly:", item.color, style);
        }

        // Force a repaint
        targetDiv.style.opacity = '0.99';
        setTimeout(() => {
          targetDiv.style.opacity = '1';
        }, 10);
      } else {
        console.warn("Could not find design space content by ID");

        // Fallback to the old method if ID-based approach fails
        const designSpace = document.querySelector('.design-space');
        if (designSpace) {
          const contentDiv = designSpace.querySelector('div[style*="position: relative"]');
          if (contentDiv) {
            console.log("Found design space content by query:", contentDiv);

            // Use the same approach for all types
            if (item.type === 'solid') {
              // Apply directly as background-color for solid colors
              contentDiv.style.setProperty('background-color', item.color, 'important');
              contentDiv.style.setProperty('background-image', 'none', 'important');

              // Log the current style
              console.log("Fallback: Current style before direct attribute setting:", contentDiv.getAttribute('style'));

              // Also try setting the attribute directly
              contentDiv.setAttribute('style', contentDiv.getAttribute('style') +
                `; background-color: ${item.color} !important; background-image: none !important;`);

              // Log the style after setting
              console.log("Fallback: Style after direct attribute setting:", contentDiv.getAttribute('style'));
              console.log("Applied solid color directly to fallback:", item.color);
            } else if (item.type === 'gradient') {
              contentDiv.style.setProperty('background-image', item.color, 'important');

              // Also try setting the attribute directly
              contentDiv.setAttribute('style', contentDiv.getAttribute('style') +
                `; background-image: ${item.color} !important;`);
            } else if (item.type === 'pattern' || item.type === 'texture' || item.type === 'photo') {
              const style = item.style || {};
              contentDiv.style.setProperty('background-image', item.color, 'important');

              if (style.backgroundSize)
                contentDiv.style.setProperty('background-size', style.backgroundSize, 'important');
              if (style.backgroundPosition)
                contentDiv.style.setProperty('background-position', style.backgroundPosition, 'important');
              if (style.backgroundRepeat)
                contentDiv.style.setProperty('background-repeat', style.backgroundRepeat, 'important');
            }

            // Force a repaint
            contentDiv.style.opacity = '0.99';
            setTimeout(() => {
              contentDiv.style.opacity = '1';
            }, 10);
          }
        }
      }
    } catch (error) {
      console.error("Error applying background:", error);
    }
  };

  return (
    <div className="canva-backgrounds">
      <h3 className="text-xl font-semibold mb-4 text-gray-800">Backgrounds</h3>

      {/* Search */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search backgrounds..."
            className="w-full p-3 pr-12 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="mb-6">
        <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
        <div className="grid grid-cols-5 gap-3 overflow-x-auto hide-scrollbar pb-2">
          {backgroundCategories.map(category => {
            // Define category icons (using emoji for simplicity and reliability)
            let bgGradient = '';

            switch(category.id) {
              case 'solid-colors':
                bgGradient = 'from-blue-500 to-indigo-600';
                break;
              case 'gradients':
                bgGradient = 'from-indigo-500 to-purple-600';
                break;
              case 'patterns':
                bgGradient = 'from-purple-500 to-pink-600';
                break;
              case 'textures':
                bgGradient = 'from-pink-500 to-rose-600';
                break;
              case 'photos':
                bgGradient = 'from-amber-400 to-orange-500';
                break;
              default:
                bgGradient = 'from-gray-400 to-gray-500';
            }

            return (
              <div
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                  activeCategory === category.id
                    ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg'
                    : 'hover:shadow-md hover:scale-105'
                }`}
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>

                {/* Content */}
                <div className="relative p-3 flex flex-col items-center justify-center h-20">
                  <span className="text-2xl mb-1 text-white">{category.icon}</span>
                  <span className="text-xs font-medium text-white text-center truncate w-full">
                    {category.name}
                  </span>
                </div>

                {/* Active indicator */}
                {activeCategory === category.id && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Backgrounds Grid */}
      <div className="grid grid-cols-3 gap-3">
        {filteredItems?.map(item => (
          <div
            key={item.id}
            className="bg-gray-50 rounded-lg p-3 aspect-square flex flex-col items-center justify-center cursor-pointer hover:bg-gray-100 hover:shadow-sm transition-all duration-200 border border-gray-100 overflow-hidden"
            onClick={() => handleApplyBackground(item)}
          >
            {item.type === 'solid' && (
              <div
                className="w-14 h-14 rounded-md mb-2 shadow-sm"
                style={{ backgroundColor: item.color }}
              ></div>
            )}
            {item.type === 'gradient' && (
              <div
                className="w-14 h-14 rounded-md mb-2 shadow-sm"
                style={{ background: item.color }}
              ></div>
            )}
            {(item.type === 'pattern' || item.type === 'texture' || item.type === 'photo') && (
              <div
                className="w-14 h-14 rounded-md mb-2 shadow-sm bg-cover bg-center"
                style={{
                  background: item.color,
                  ...(item.style || {})
                }}
              ></div>
            )}
            <span className="text-xs font-medium text-center text-gray-700">{item.name}</span>
          </div>
        ))}

        {filteredItems?.length === 0 && (
          <div className="col-span-3 py-10 text-center text-gray-500">
            <div className="text-3xl mb-2">🔍</div>
            <div className="font-medium">No backgrounds found</div>
            <div className="text-xs mt-1">Try a different search term or category</div>
          </div>
        )}
      </div>
    </div>
  );
}

export default BackgroundSettings;
