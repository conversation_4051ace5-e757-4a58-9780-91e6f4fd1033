/* Override styles to remove transparency from design space */

.design-space {
  position: relative;
  overflow: hidden;
  background-color: #ffffff !important; /* Solid white background */
  background-image: none !important; /* Remove grid pattern */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important; /* Stronger shadow */
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  transform-style: preserve-3d;
  perspective: 1200px;
  transform: rotateX(0deg) !important; /* Remove 3D effect */
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  z-index: 10 !important; /* Ensure design space is above background elements */
}

/* Hide design elements from the design area */
.design-space-container {
  position: relative;
  z-index: 5;
}

.design-elements-container {
  z-index: 1;
  pointer-events: none;
}

/* Ensure design elements don't appear inside the design space */
.design-space .design-element {
  display: none !important;
}

/* Smart positioning for element controls */
.draggable-element.top-edge .element-controls {
  top: auto !important;
  bottom: -55px !important;
}

.draggable-element.top-edge .element-controls::after {
  bottom: auto !important;
  top: -6px !important;
}

.draggable-element.right-edge .element-controls {
  right: auto !important;
  left: -10px !important;
}

.draggable-element.right-edge .element-controls::after {
  right: auto !important;
  left: 15px !important;
}

/* Remove transparent overlays */
.design-space::before {
  display: none !important;
}

/* Keep shadow but make it more solid */
.design-space::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 5%;
  right: 5%;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.3) !important;
  filter: blur(8px);
  border-radius: 50%;
  z-index: -1;
  transform-origin: center;
  animation: none !important; /* Remove animation */
}

/* Remove hover effects that change transparency */
.design-space:hover {
  transform: none !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
}

/* Make corner marks more visible */
.design-space .corner-mark {
  border-color: rgba(0, 0, 0, 0.4) !important;
}

/* Ensure the design area container is solid */
.design-space > div {
  background-color: white !important;
  opacity: 1 !important;
}
