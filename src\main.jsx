import React from 'react'
import ReactDOM from 'react-dom/client'
import { I18nextProvider } from 'react-i18next';
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query';

import i18n from './config/i18n';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import AdminRoutes from './routes/AdminRoutes'; 
import UserRoutes from './routes/UserRoutes'; 


import './index.css'


import RoutesContainer from './routes/RoutesContainer';
import { DataTableProvider } from './contexts/DataTableContext';
import { GlobalProvider } from './contexts/GlobalContext';

const queryClient = new QueryClient();


ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <QueryClientProvider client={ queryClient }>
      <I18nextProvider i18n={ i18n }>
        <GlobalProvider>
          <DataTableProvider>
                <BrowserRouter>
                  <AdminRoutes />
                </BrowserRouter>
                <BrowserRouter>
                  <UserRoutes />
                </BrowserRouter>
                <BrowserRouter>
                  <RoutesContainer />
                </BrowserRouter>
                
          </DataTableProvider>
        </GlobalProvider>
      </I18nextProvider>
    </QueryClientProvider>
  </React.StrictMode>,
)
