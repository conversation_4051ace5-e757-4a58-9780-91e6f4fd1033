import { useMutation, useQuery } from 'react-query';
import axiosInstance from '../config/Axios';

import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { usersTableConfig } from '@constants';
import { handleErrors } from '@utils/helper';

//--------------create user-------------- //
const createUser = async (payload) => {
    const { data } = await axiosInstance.post("/datatable/users/create", payload);

    return data.data;
    
}



export const useCreateUserMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();
    const { setLazyParams  } = useDataTableContext();
 
    return useMutation(createUser, {
        onSuccess: async () => {
            setLazyParams(prev => ({ ...prev, ...usersTableConfig }))
            dialogHandler("addMember")
            showToast("success", "Success", "Member added successfully!")
        
         },
        onError: (error) => {
            handleErrors(showToast, error)
 
        }
    })
}

//--------------update user-------------- //
const updateUser = async (payload) => {
    const { data } = await axiosInstance.post("/datatable/users/update", payload);

    return data.data;
}

export const useUpdateUserMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();
    const { setLazyParams  } = useDataTableContext();
 
    return useMutation(updateUser, {
        onSuccess: async () => {
            setLazyParams(prev => ({ ...prev, ...usersTableConfig }))
            dialogHandler("addMember")
            showToast("success", "Success", "Member updated successfully!")
        
         },
        onError: (error) => {
            handleErrors(showToast, error)
 
        }
    })
}

//--------------get user-------------- //
const getUser = async (id) => {
    const { data } = await axiosInstance.get(`/users/${id}`);

    return data.data;
}

export const fetchUser = (id) => {
    const { showToast } = useGlobalContext();
    let { isLoading, data, error, isError } = useQuery('getUser', () => getUser(id));

    if (isError) {
        showToast("error", "Fetch Types ", error.response?.data?.message)
    }

    return { isLoading, data };
}

//--------------delete user-------------- //
const deleteUser = async (payload) => {
    const { data } = await axiosInstance.delete(`/users/${payload.id}`);

    return data;
}

export const useDeleteUserMutation = () => {
    const { showToast } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();

    return useMutation(deleteUser, {
        onSuccess: async () => {
            setLazyParams(prev => ({ ...prev, ...usersTableConfig }))
        },
        onError: (error) => handleErrors(showToast, error)
    })
}