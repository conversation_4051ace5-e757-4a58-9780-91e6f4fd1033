import { useLocation } from 'react-router-dom';
import { <PERSON> } from 'react-router-dom';

import { useGlobalContext } from '@contexts/GlobalContext';
import { _menuTabs } from '@constants/menuTabs';
import logo from "@images/Logo.svg"

import { Image } from 'primereact/image';

import Banner from './Banner';

function SideMenu() {
  const { userType } = useGlobalContext();
  const location = useLocation();
  const routeName = location.pathname;
  const isGroupMembersPage = routeName === '/members/group';

  const menuTabs = _menuTabs[userType] || _menuTabs.user;

  return (
    <aside className='bg-[white] p-4 flex flex-col items-center justify-start h-[100vh] w-full'>
      <Link to={`/${userType}/dashboard`}>
        <img src={logo} alt="Ink Null Logo" style={{ width: '100%' }} />
      </Link>

      <ul className='w-full mt-10 m-5'>
        {
          menuTabs.map((tab, index) => {
            // Special case for Groups and Members menu items
            let isActive = false;
            if (isGroupMembersPage) {
              // If we're on the group members page, activate the Groups menu item
              isActive = tab.activeKey === 'groups';
            } else {
              // Normal case - check if the route includes the activeKey
              isActive = routeName.includes(tab.activeKey);
            }

            return !tab.onlyAdmin || (tab.onlyAdmin && userType === "admin") ? (
              <Link to={tab.route} key={index}>
                <li className={`flex p-1 items-center ${isActive ? "active_tab" : "text-[#939393]"}`}>
                  <span className='m-3'>
                    {tab.icon}
                  </span>
                  {tab?.title}
                </li>
              </Link>
            ) : null;
          })
        }
      </ul>

      {/*Profile Stuff here*/}
      <div className='mt-auto w-full'>
      <Banner/>
      </div>
    </aside>
  )
}

export default SideMenu;
