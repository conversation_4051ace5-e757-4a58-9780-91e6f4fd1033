import React, { useEffect, useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import white_logo from "@images/white_logo.svg";
import {
    IoShapesOutline,
    IoColorPaletteOutline,
    IoImageOutline,
    IoTextOutline,
    IoLayersOutline,
    IoGridOutline
} from 'react-icons/io5';
import {
    FaPencilRuler,
    FaPalette,
    FaFont,
    FaImage
} from 'react-icons/fa';
import {
    RxText,
    RxImage,
    RxPencil2
} from 'react-icons/rx';

const DesignSpaceBackground = () => {
    const { cardType } = useDesignSpace();
    const [particles, setParticles] = useState([]);
    const [beams, setBeams] = useState([]);
    const [designElements, setDesignElements] = useState([]);

    // Generate random particles for the background
    useEffect(() => {
        const generateParticles = () => {
            const newParticles = [];
            const colors = ['blue', 'purple', 'pink', 'indigo', 'cyan'];

            for (let i = 0; i < 10; i++) {
                newParticles.push({
                    id: i,
                    x: Math.random() * 100,
                    y: Math.random() * 100,
                    size: 20 + Math.random() * 40,
                    color: colors[Math.floor(Math.random() * colors.length)],
                    delay: Math.random() * 10,
                    duration: 15 + Math.random() * 10
                });
            }

            setParticles(newParticles);
        };

        const generateBeams = () => {
            const newBeams = [];

            for (let i = 0; i < 5; i++) {
                newBeams.push({
                    id: i,
                    x: 10 + Math.random() * 80,
                    rotation: -45 + Math.random() * 90,
                    delay: Math.random() * 2,
                    opacity: 0.05 + Math.random() * 0.1
                });
            }

            setBeams(newBeams);
        };

        // Generate design elements
        const generateDesignElements = () => {
            const newElements = [];
            const icons = [
                <IoShapesOutline size={20} />,
                <IoColorPaletteOutline size={20} />,
                <IoImageOutline size={20} />,
                <IoTextOutline size={20} />,
                <IoLayersOutline size={20} />,
                <IoGridOutline size={20} />,
                <RxText size={20} />,
                <RxImage size={20} />,
                <RxPencil2 size={20} />,
                <FaPencilRuler size={20} />,
                <FaPalette size={20} />,
                <FaFont size={20} />,
                <FaImage size={20} />
            ];

            // Generate 30 random design elements
            for (let i = 0; i < 30; i++) {
                const x = Math.random() * 100; // % position
                const y = Math.random() * 100; // % position
                const size = Math.random() * 0.5 + 0.5; // scale factor 0.5-1.0
                const opacity = Math.random() * 0.15 + 0.05; // 0.05-0.2
                const rotation = Math.random() * 360; // 0-360 degrees
                const delay = Math.random() * 5; // 0-5s delay
                const duration = Math.random() * 10 + 10; // 10-20s duration
                const iconIndex = Math.floor(Math.random() * icons.length);

                newElements.push({
                    id: `design-element-${i}`,
                    x,
                    y,
                    size,
                    opacity,
                    rotation,
                    delay,
                    duration,
                    icon: icons[iconIndex]
                });
            }

            setDesignElements(newElements);
        };

        generateParticles();
        generateBeams();
        generateDesignElements();
    }, []);

    return (
        <div className="absolute inset-0 overflow-hidden stone-wall-background">
            {/* Animated Particles */}
            <div className="absolute inset-0 opacity-20">
                {particles.map(particle => (
                    <div
                        key={particle.id}
                        className="absolute rounded-full filter blur-3xl animate-float"
                        style={{
                            top: `${particle.y}%`,
                            left: `${particle.x}%`,
                            width: `${particle.size}px`,
                            height: `${particle.size}px`,
                            animationDelay: `${particle.delay}s`,
                            animationDuration: `${particle.duration}s`,
                            backgroundColor: particle.color === 'blue' ? '#60a5fa' :
                                            particle.color === 'purple' ? '#a78bfa' :
                                            particle.color === 'pink' ? '#f472b6' :
                                            particle.color === 'indigo' ? '#818cf8' :
                                            '#67e8f9' // cyan
                        }}
                    ></div>
                ))}
            </div>

            {/* Light Beams - Enhanced */}
            <div className="absolute inset-0 opacity-15">
                {beams.map(beam => (
                    <div
                        key={beam.id}
                        className="absolute top-0 w-1.5 h-full bg-white animate-pulse-enhanced"
                        style={{
                            left: `${beam.x}%`,
                            transform: `rotate(${beam.rotation}deg)`,
                            animationDelay: `${beam.delay}s`,
                            filter: 'blur(8px) brightness(1.2)',
                            opacity: beam.opacity * 1.5
                        }}
                    ></div>
                ))}
            </div>

            {/* Animated Gradient Overlay */}
            <div
                className="absolute inset-0 opacity-30"
                style={{
                    background: 'linear-gradient(45deg, rgba(76, 29, 149, 0.3), rgba(124, 58, 237, 0.3), rgba(167, 139, 250, 0.3))',
                    backgroundSize: '400% 400%',
                    animation: 'gradientAnimation 15s ease infinite'
                }}
            ></div>

            {/* Design Elements - Icons and Shapes - Hidden from design area */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none design-elements-container">
                {/* Design elements are now hidden from the design area but still visible in the background */}
                {designElements.map((element) => (
                    <div
                        key={element.id}
                        className="absolute design-element"
                        style={{
                            left: `${element.x}%`,
                            top: `${element.y}%`,
                            opacity: element.opacity * 1.5, // Increased opacity
                            color: 'rgba(255, 255, 255, 0.8)', // Brighter color
                            transform: `rotate(${element.rotation}deg) scale(${element.size})`,
                            animationDelay: `${element.delay}s`,
                            animationDuration: `${element.duration}s`,
                            zIndex: 1,
                            '--rotation': `${element.rotation}deg`,
                            filter: `drop-shadow(0 0 4px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 8px rgba(255, 255, 255, 0.4))`, // Enhanced glow
                            transition: 'all 0.5s ease'
                        }}
                    >
                        {element.icon}
                    </div>
                ))}
            </div>

            {/* Neon Logo Watermark */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="relative w-full max-w-4xl neon-logo-container">
                    {/* Base logo layer */}
                    <img
                       src={white_logo}
                        alt="Logo Watermark"
                        className="w-full opacity-30"
                    />

                    {/* Glow effect layers */}
                    <img
                        src={white_logo}
                        alt=""
                        className="absolute top-0 left-0 w-full neon-glow-blue"
                        style={{
                            filter: 'blur(5px) brightness(1.5)',
                            opacity: 0.6,
                            mixBlendMode: 'screen'
                        }}
                    />

                    <img
                        src={white_logo}
                        alt=""
                        className="absolute top-0 left-0 w-full neon-glow-purple"
                        style={{
                            filter: 'blur(10px) brightness(1.2)',
                            opacity: 0.4,
                            mixBlendMode: 'screen'
                        }}
                    />

                    {/* Bright core */}
                    <img
                        src={white_logo}
                        alt=""
                        className="absolute top-0 left-0 w-full neon-core"
                        style={{
                            filter: 'brightness(2)',
                            opacity: 0.8
                        }}
                    />
                </div>
            </div>

            {/* CSS Animation for the gradient */}
            <style jsx>{`
                @keyframes gradientAnimation {
                    0% { background-position: 0% 50% }
                    50% { background-position: 100% 50% }
                    100% { background-position: 0% 50% }
                }

                @keyframes float {
                    0% { transform: translate(0, 0) }
                    50% { transform: translate(15px, 15px) }
                    100% { transform: translate(0, 0) }
                }

                .animate-float {
                    animation: float 20s ease infinite;
                }

                .animate-pulse {
                    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
                }

                @keyframes pulse {
                    0%, 100% { opacity: 0.1 }
                    50% { opacity: 0.3 }
                }

                .animate-pulse-enhanced {
                    animation: pulse-enhanced 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
                }

                @keyframes pulse-enhanced {
                    0%, 100% { opacity: 0.15; filter: blur(8px) brightness(1.0); }
                    50% { opacity: 0.4; filter: blur(10px) brightness(1.5); }
                }

                /* Stone wall background */
                .stone-wall-background {
                    background-color: #2d3748;
                    background-image:
                        linear-gradient(335deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(155deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(335deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(155deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(335deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(155deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(50, 50, 50, 0.4) 1px, transparent 1px),
                        linear-gradient(180deg, rgba(50, 50, 50, 0.4) 1px, transparent 1px),
                        radial-gradient(circle at 50% 50%, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0) 60%);
                    background-size:
                        20px 20px,
                        20px 20px,
                        100px 100px,
                        100px 100px,
                        200px 200px,
                        200px 200px,
                        40px 40px,
                        40px 40px,
                        100% 100%;
                    background-position:
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        center center;
                    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5);
                }

                /* Neon logo animations */
                .neon-logo-container {
                    animation: neon-flicker 5s infinite alternate-reverse;
                }

                .neon-glow-blue {
                    animation: neon-blue-pulse 2s infinite alternate;
                }

                .neon-glow-purple {
                    animation: neon-purple-pulse 3s infinite alternate;
                }

                .neon-core {
                    animation: neon-core-pulse 1s infinite alternate;
                }

                @keyframes neon-flicker {
                    0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
                        opacity: 0.95;
                        filter: brightness(1) drop-shadow(0 0 15px rgba(255,255,255,0.7));
                    }
                    20%, 24%, 55% {
                        opacity: 0.6;
                        filter: brightness(0.8) drop-shadow(0 0 5px rgba(255,255,255,0.4));
                    }
                }

                @keyframes neon-blue-pulse {
                    0% {
                        filter: blur(5px) brightness(1.5) drop-shadow(0 0 5px #60a5fa);
                        opacity: 0.6;
                    }
                    100% {
                        filter: blur(7px) brightness(2) drop-shadow(0 0 15px #60a5fa);
                        opacity: 0.8;
                    }
                }

                @keyframes neon-purple-pulse {
                    0% {
                        filter: blur(10px) brightness(1.2) drop-shadow(0 0 10px #a78bfa);
                        opacity: 0.4;
                    }
                    100% {
                        filter: blur(15px) brightness(1.5) drop-shadow(0 0 20px #a78bfa);
                        opacity: 0.6;
                    }
                }

                @keyframes neon-core-pulse {
                    0% {
                        filter: brightness(1.8);
                        opacity: 0.7;
                    }
                    100% {
                        filter: brightness(2.2);
                        opacity: 1;
                    }
                }

                /* Design elements animations */
                .design-element {
                    animation: float-element 15s ease infinite, glow-pulse 3s ease-in-out infinite alternate;
                    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
                }

                @keyframes glow-pulse {
                    0% { filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5)); }
                    100% { filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8)); }
                }

                @keyframes float-element {
                    0% { transform: translate(0, 0) rotate(var(--rotation)); }
                    25% { transform: translate(10px, 10px) rotate(calc(var(--rotation) + 5deg)); }
                    50% { transform: translate(0, 20px) rotate(var(--rotation)); }
                    75% { transform: translate(-10px, 10px) rotate(calc(var(--rotation) - 5deg)); }
                    100% { transform: translate(0, 0) rotate(var(--rotation)); }
                }
            `}</style>
        </div>
    );
};

export default DesignSpaceBackground;
