
import { useEffect, useState, useCallback  } from 'react'
import { Link, useNavigate } from 'react-router-dom';
import { useRef } from 'react';

import { useDeleteGroupMutation, useGetGroupMember } from '@quires'
import { groupsTableConfig, defaultTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';

import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';

import { TfiTrash } from "react-icons/tfi";
import { FaRegEye } from 'react-icons/fa';
import { FaSearch } from "react-icons/fa";


import GroupForm from '../../Backages/CreateGroupForm';

const backendUrl = import.meta.env.VITE_BACKEND_URL;
const token = localStorage.getItem("token");

const statusStyles = {
    printed: "bg-[#22C55E] ",
    active: "bg-[#22C55E] ",
    inactive: "bg-[#dc2626] ",
    unprinted: "bg-[#64748B] ",
    onProgress: "bg-[#D97706] ",
    draft: "bg-[#dc2626] ",
    published: "bg-[#22C55E] ",
}

function GroupsDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useDataTableContext();
    const { setSelectedMembers , openDialog, dialogHandler } = useGlobalContext();

    console.log('📌 API Response groups:', data); // Log the API response

    const [isCreateGroupModalOpen, setisCreateGroupModalOpen] = useState(false);
    const openCreateCardModal = () => {
        console.log("Modal opened"); 
        setisCreateGroupModalOpen(true);
      };

    const [isEditGroupModalOpen, setIsEditGroupModalOpen] = useState(false);
    const [groupBeingEdited, setGroupBeingEdited] = useState(null); // State to hold the rowData for editing

    const [groupMembers, setGroupMembers] = useState('Hover to load members');

    const getGroupMember = useGetGroupMember()
    const deleteRow = useDeleteGroupMutation();

    const [searchQuery, setSearchQuery] = useState('');
    const [selectedRow, setSelectedRow] = useState({});
    const navigate = useNavigate();

    const toast = useRef(null);

        // Add debounced search handler
    useEffect(() => {
        const timeout = setTimeout(() => {
            setLazyParams(prev => ({
                ...prev,
                filters: {
                    ...prev.filters,
                    title: { value: searchQuery, matchMode: 'contains' }
                }
            }));
        }, 300);
    
            return () => clearTimeout(timeout);
        }, [searchQuery]);

    useEffect(() => {
        setLazyParams({ ...defaultTableConfig, ...groupsTableConfig })
    }, [])

    const deleteAdHandler = async (rowData) => {
        await deleteRow.mutateAsync({
            id: rowData?.id,
        }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev, ...groupsTableConfig }));
                
                // Show success toast
                // toast.current.show({
                //     severity: 'success',
                //     summary: 'Success',
                //     detail: 'Group deleted successfully',
                //     life: 3000
                // });
            },
            onError: () => {
                // Show error toast
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to delete group',
                    life: 3000
                });
            }
        })
    }

    async function getGroupMemberNames(groupToEdit) {
        const getUserDisplayName = (user) => {
            return user.name || user.username || user.email || `User ${user.id}`;
        };
    
        try {
            const groupsResponse = await fetch(`${backendUrl}/groups?users=true`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    Accept: "application/json",
                },
            });
    
            if (!groupsResponse.ok) {
                console.error("Failed to fetch group users, using default selection");
                return 'No members';
            }
    
            const groupsData = await groupsResponse.json();
            const currentGroup = groupsData.data?.find(group => group.id === groupToEdit);
            if (!currentGroup?.users) return 'No members';
    
            const groupUsers = currentGroup.users.map(user => getUserDisplayName(user));
    
            const displayNames = groupUsers.slice(0, 5).join('\n');
            return groupUsers.length > 5 ? `${displayNames}\n...` : displayNames;
        } catch (error) {
            console.error("Error fetching group members:", error);
            return 'No members';
        }
    }
    
    const handleTooltipShow = async (groupToEdit) => {
        try {
          // Only fetch if we haven't loaded yet
          if (groupMembers === 'Hover to load members') {
            setGroupMembers('Loading members...');
            const members = await getGroupMemberNames(groupToEdit);
            setGroupMembers(members);
          }
        } catch {
          setGroupMembers('Failed to load members');
        }
      };

    // --- Success Handler (called by GroupForm on successful create/update) ---
    const handleSuccess = useCallback(() => {
        console.log("GroupForm reported success. Refreshing data...");
        // Refresh data by triggering the DataTable context's handler/refetch mechanism
        // Resetting page to 0 ensures user sees the newly created/updated item if on first page
        setLazyParams(prev => ({ ...prev, page: 0, ...groupsTableConfig }));
        // Optionally clear edit state, though GroupForm closing should handle this too
        setGroupBeingEdited(null);
    }, [setLazyParams]); // Depends on setLazyParams to refresh

    // --- Handler to open the Edit Modal ---
    const handleEditClick = (groupData) => {
        console.log("Editing group, data received:", groupData);
        const editedData = {
          ...groupData,
          card_type_id: groupData.card_type_id || groupData.card_type?.id 
        };
        setGroupBeingEdited(editedData);
        setIsEditGroupModalOpen(true);
      };


    const handleDeleteClick = (rowData) => {
        console.log(rowData)
        confirmDialog({
            message: 'Are you sure you want to delete this group?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => deleteAdHandler(rowData),
        });
    };


    // Data Table Body Template
    const actionBodyTemplate = (rowData) => {
        return (
            <div className="flex items-center">
                {/* preview */} 
                <Tooltip target={`.preview-button-${rowData.id}`} showDelay={100}
                    content={groupMembers}
                    position="bottom"
                    onBeforeShow={() => handleTooltipShow(rowData.id)} className="fs-8" />          {/*ngl this would make for a great ddos vector, I might remove it if it proves a major enough security risk*/}
                <Link to={`/members/group?group-id=${rowData.id}`}>
                <button
                className={`btn btn-sm btn-icon preview-button-${rowData.id} me-4 flex justify-center items-center`}
                onMouseLeave={() => setGroupMembers('Hover to load members')} // Reset on mouse out
                >
                <FaRegEye size={20} />
                </button>
                </Link>



                {/* Delete  */}
                <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
                    data-pr-position="bottom"
                    data-pr-tooltip="Delete"
                    onClick={() => handleDeleteClick(rowData)}
                >
                    <TfiTrash size={20} />
                </button>
            </div>
        );
    }

    const statusBodyTemplate = (rowData) => {
    const status = rowData.status || "inactive";
    const backgroundColor = status === "active" ? "#22C55E" : 
                          status === "inactive" ? "#dc2626" : "#9ca3af";

    return (
        <span
            className="inline-block text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize"
            style={{ backgroundColor, width: "100px", textAlign: "center" }}
        >
            {status}
        </span>
    );
};

    // const printedStatusBodyTemplate = (rowData) => {
    //     return (
    //         <span className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles[rowData.print_status || "unprinted"]}`}>
    //             {rowData.print_status}
    //         </span>
    //     )
    // }


    const printedStatusBodyTemplate = (rowData) => {
        const status = rowData.print_status || "unprinted";
        const backgroundColor = status === "unprinted" ? "#9ca3af" : 
                              status === "printed" ? "#22C55E" : 
                              status === "inactive" ? "#dc2626" : "#9ca3af";
    
        return (
            <span 
                className="inline-block text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize"
                style={{ backgroundColor, width: "100px", textAlign: "center" }}
            >
                {status}
            </span>
        );
    };
    
    // const updateHandler = async (rowData) => {
    //     await getGroupMember.mutateAsync(rowData.id, {
    //         onSuccess: (data) => {
    //             setSelectedRow(rowData);
    //             setSelectedMembers({
    //                 groupData: rowData,
    //                 action: "update",
    //                 data: data,
    //             });
    //             navigate("/users/members")
    //         }
    //     })
    // }

    // const createGroup = () => {
        // setSelectedMembers({});
        // dialogHandler("createGroup");
        // console.log("Create Group Clicked")
    // }

    // const Header = () => {
    //     const activeBtn = 0; //isEmpty(selectedMembers.data);
    //     // const userRole = localStorage.getItem('user_role'); 
    //     return (
    //         <div className="w-full ">
    //             <button
    //                 className={`${activeBtn ? "gray-btn " : "main-btn"} text-md me-2 shadow-md`}
    //                 disabled={activeBtn} onClick={openCreateCardModal}>
    //                 Create Group
    //             </button>
    //         </div>
    //     )
    // }

    return (
        // <div className="flex justify-start items-start w-full mb-4">
        //    <div className="w-full max-w-fit">
        //    <button
        //    className={`${activeBtn ? "gray-btn " : "main-btn"} text-md me-2 shadow-md`}
        //    disabled={activeBtn}
        //    onClick={openCreateCardModal}
        //    >
        //    Create Group
        //    </button>
        //    </div>
        //    </div> 
        <div className="w-full min-w-full h-full flex flex-col">
            <Toast ref={toast} position="top-right" />     

            <ConfirmDialog                                       //This is a small confirm modal for the delete button
                group="headless"
                content={(options) => (
                    <div className="flex flex-col items-center p-5">
                        <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
                        <span className="text-xl font-bold mb-2">{options.message}</span>
                        <div className="flex gap-3">
                            <button className="p-button p-component" onClick={options.accept}>
                                Yes
                            </button>
                            <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                                No
                            </button>
                        </div>
                    </div>
                )}
            />

                {/* Search Bar Section */}
            <div className="flex justify-center items-center w-full mb-4 mt-1">
                <div className="flex-grow max-w-[700px] relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search by group title..."
                        className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md 
                                    focus:outline-none focus:ring-2 focus:ring-blue-300 
                                    focus:border-blue-300 transition-all duration-200"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        />
                </div>
            </div>

             {/* Container for Table and Modals */}

                    {/* --- Render Create Group Modal --- */}
                    {/* Conditionally rendered ONLY when isCreateGroupModalOpen is true */}
                    {isCreateGroupModalOpen && (
                        <GroupForm
                            isModalOpen={isCreateGroupModalOpen}
                            setIsModalOpen={setisCreateGroupModalOpen} // Pass the setter for create modal
                            onSuccess={handleSuccess} // Pass the common success handler
                            // groupToEdit prop is omitted, so it runs in CREATE mode
                        />
                    )}

                    {/* --- Render Edit Group Modal --- */}
                    {/* Conditionally rendered ONLY when isEditGroupModalOpen is true AND groupBeingEdited has data */}
                    {isEditGroupModalOpen && groupBeingEdited && (
                        <GroupForm
                            isModalOpen={isEditGroupModalOpen}
                            setIsModalOpen={setIsEditGroupModalOpen} // Pass the setter for edit modal
                            onSuccess={handleSuccess} // Pass the common success handler
                            groupToEdit={groupBeingEdited} // Pass the data for the group to be edited
                        />
                    )}

                {/* --- PrimeReact DataTable --- */}
                <div className="flex-grow h-full">
                    <DataTable

                        lazy
                        responsiveLayout="stack"
                        breakpoint="960px"
                        dataKey="id"
                        paginator
                        className="border-t-0"
                        value={data}
                        first={lazyParams?.first}
                        rows={lazyParams?.rows}
                        rowsPerPageOptions={[5, 25, 50, 100]}
                        totalRecords={totalRecords}
                        onPage={dataHandler}
                        onSort={dataHandler}
                        sortField={lazyParams?.sortField}
                        sortOrder={lazyParams?.sortOrder}
                        onFilter={dataHandler}
                        filters={lazyParams?.filters}
                        loading={loading}
                        scrollable
                        scrollHeight="100%"
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                        header={null}       
                    >
                    {/* <Column field="name" header="Name" className='text-center' filter sortable /> */}
                    <Column field="title" header="Title" className='text-center' filter sortable />
                    <Column field="description" header="Description" className='text-center' filter sortable />
                    <Column field="card_type_name" header="Card Type" className='text-center' filter sortable />

                    {/* <Column body={printedStatusBodyTemplate} field="print_status"  header="Print Status" className='text-center' filter sortable /> */}
                    <Column body={statusBodyTemplate} field="status" header="Status" className='text-center' filter sortable />
                    <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '8rem' }}></Column>
                </DataTable>
                </div>
            </div>
    )
}

export default GroupsDataTable