import React, { useEffect, useRef, useState, useCallback } from 'react';
import { isEmpty } from 'lodash';
import parse from 'html-react-parser';
import { Dialog } from 'primereact/dialog';
import { motion } from 'framer-motion';
import { saveAs } from 'file-saver';
import { FaFileExport, FaFileImport } from 'react-icons/fa';
import TemplateImageHeader from './TemplateFilter';
import AssignGroupDialog from '../Groups/AssignGroupDialog';
import AddMemberDialog from './AddMemberDialog';
import GroupForm from '../../Backages/CreateGroupForm';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { usersTableConfig, defaultTableConfig } from '@constants';
import { useDeleteUserMutation } from '@quires/user';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useQueryParams } from '@utils/helper';
import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';

import ImportDialog from './ImportDialog';
import axiosInstance from "../../../../config/Axios";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { FaRegEye } from 'react-icons/fa';
import Element from '../../DesignSpace/components/Element';

import profile_img from "@images/Profile_img.jpg"

import { FaSearch } from 'react-icons/fa';

const statusStyles = {
    printed: "bg-[#22C55E] ",
    unprinted: "bg-[#64748B] ",
    onProgress: "bg-[#D97706] ",
}

function MemberDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, loading } = useDataTableContext();
    const { dialogHandler, openDialog, selectedMembers, setSelectedMembers } = useGlobalContext();
    const queryParams = useQueryParams();
    const groupID = queryParams.get("group-id");
    const designID = queryParams.get("design-id");
    const deleteRow = useDeleteUserMutation();
    const [importDialogVisible, setImportDialogVisible] = useState(false);
    const [selectedMember, setSelectedMember] = useState();
    const [actionType, setActionType] = useState("create");
    const userType = localStorage.getItem('user_type');
    const activeBtn = isEmpty(selectedMembers.data);
    const [memberCards, setMemberCards] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [isCreateGroupModalOpen, setIsCreateGroupModalOpen] = useState(false);

    const toast = useRef(null);

    // States for card view
    const [selectedCard, setSelectedCard] = useState(null);
    const [allCards, setAllCards] = useState([]);
    const [currentCardIndex, setCurrentCardIndex] = useState(0);
    const [isCardViewOpen, setIsCardViewOpen] = useState(false);
    const [isFlipped, setIsFlipped] = useState(false);

    const dataHandler = useCallback((e = {}) => {
        // Avoid creating a new filters object on every call if nothing changed
        const nameFilter = { value: searchQuery, matchMode: 'contains' };

        setLazyParams(prev => {
            // Only update if something actually changed
            const newFirst = e?.first !== undefined ? e.first : prev.first;
            const newRows = e?.rows !== undefined ? e.rows : prev.rows;
            const newPage = e?.first !== undefined ? Math.floor(e.first / (e.rows || prev.rows)) : prev.page;
            const newSortField = e?.sortField !== undefined ? e.sortField : prev.sortField;
            const newSortOrder = e?.sortOrder !== undefined ? e.sortOrder : prev.sortOrder;

            // Check if filters need to be updated
            const prevNameFilter = prev.filters?.name?.value;
            const filtersChanged = prevNameFilter !== searchQuery;

            // Only create a new object if something changed
            if (
                newFirst !== prev.first ||
                newRows !== prev.rows ||
                newPage !== prev.page ||
                newSortField !== prev.sortField ||
                newSortOrder !== prev.sortOrder ||
                filtersChanged
            ) {
                return {
                    ...prev,
                    filters: {
                        ...prev.filters,
                        name: nameFilter
                    },
                    first: newFirst,
                    rows: newRows,
                    page: newPage,
                    sortField: newSortField,
                    sortOrder: newSortOrder,
                };
            }

            // Return the same object if nothing changed
            return prev;
        });
    }, [searchQuery, setLazyParams]);

    const handleImportSuccess = () => {
        setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
        toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Users imported successfully',
            life: 3000
        });
    };

    const handleGroupSuccess = () => {
        setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
        setIsCreateGroupModalOpen(false);
    };
    useEffect(() => {
        const fetchMemberCards = async () => {
            try {
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('user_id');

                const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    }
                });

                if (response.data && response.data.cards) {
                    setMemberCards(response.data.cards);
                }
            } catch (error) {
                console.error('Error fetching member cards:', error);
            }
        };

        fetchMemberCards();
    }, []);

    useEffect(() => {
        setLazyParams({
            ...defaultTableConfig,
            ...usersTableConfig,
            groupID: groupID,
            designID: designID
        })
    }, [groupID, designID, setLazyParams])

    // Use debounce for search query changes to avoid excessive API calls
    useEffect(() => {
        // Skip initial render
        const timeout = setTimeout(() => {
            // Only call dataHandler if searchQuery actually changed
            if (lazyParams.filters?.name?.value !== searchQuery) {
                dataHandler();
            }
        }, 500); // Increased debounce time to 500ms for better performance

        return () => clearTimeout(timeout);
    }, [searchQuery, dataHandler, lazyParams.filters?.name?.value]);


    const createMember = () => {
        setActionType("create")
        setSelectedMember({});
        dialogHandler("addMember");
    }

    const createGroup = () => {
        if (!isEmpty(selectedMembers.data)) {
            console.log("Selected members for group creation:", selectedMembers.data);
            setIsCreateGroupModalOpen(true);
        }
    }

    const editMember = (data) => {
        setActionType("update");
        const updatedData = { ...data };
        delete updatedData.role;
        delete updatedData.group_permission;
        delete updatedData.design;
        setSelectedMember(updatedData);
        dialogHandler("addMember");
    }


    const handleExport = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axiosInstance.get('users/export', {
                responseType: 'blob',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            saveAs(blob, `users_export_${new Date().toISOString().split('T')[0]}.xlsx`);

            toast.success('Data exported successfully');
        } catch (error) {
            console.error('Error exporting data:', error);
            toast.error('Failed to export data');
        }
    };

    const deleteAdHandler = async (rowData) => {
        await deleteRow.mutateAsync({
            id: rowData?.id,
        }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev, ...usersTableConfig }));

                // Show success toast
                toast.current.show({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Member deleted successfully',
                    life: 3000
                });
            },
            onError: () => {
                // Show error toast
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to delete member',
                    life: 3000
                });
            }
        })
    }

    // Removed unused deleteRowHandler function

    const viewMemberCard = (rowData) => {
        if (rowData.cards && rowData.cards.length > 0) {
            setAllCards(rowData.cards);
            setCurrentCardIndex(0);
            setSelectedCard(rowData.cards[0]);
            console.log("ur crds",rowData.cards)
            setIsCardViewOpen(true);
        }
    };

    // Navigation functions for carousel
    const goToNextCard = () => {
        if (currentCardIndex < allCards.length - 1) {
            const nextIndex = currentCardIndex + 1;
            setCurrentCardIndex(nextIndex);
            setSelectedCard(allCards[nextIndex]);
            setIsFlipped(false); // Reset flip state when changing cards
        }
    };

    const goToPreviousCard = () => {
        if (currentCardIndex > 0) {
            const prevIndex = currentCardIndex - 1;
            setCurrentCardIndex(prevIndex);
            setSelectedCard(allCards[prevIndex]);
            setIsFlipped(false); // Reset flip state when changing cards
        }
    };

    const goToCard = (index) => {
        if (index >= 0 && index < allCards.length) {
            setCurrentCardIndex(index);
            setSelectedCard(allCards[index]);
            setIsFlipped(false); // Reset flip state when changing cards
        }
    };

    // Keyboard navigation
    useEffect(() => {
        const handleKeyPress = (event) => {
            if (!isCardViewOpen || allCards.length <= 1) return;

            switch (event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    goToPreviousCard();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    goToNextCard();
                    break;
                case 'Escape':
                    event.preventDefault();
                    setIsCardViewOpen(false);
                    setIsFlipped(false);
                    setCurrentCardIndex(0);
                    setAllCards([]);
                    break;
                default:
                    break;
            }
        };

        if (isCardViewOpen) {
            document.addEventListener('keydown', handleKeyPress);
        }

        return () => {
            document.removeEventListener('keydown', handleKeyPress);
        };
    }, [isCardViewOpen, allCards.length, currentCardIndex]);

    const handleDeleteClick = (rowData) => {
        console.log(rowData)
        confirmDialog({
            message: 'Are you sure you want to remove this member?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => deleteAdHandler(rowData),
        });
    };


    const actionBodyTemplate = (rowData) => {
        const currentUserId = localStorage.getItem('user_id');



        return (
            <>
                <div className="d-inline-block text-nowrap">
                    {/* Eye Icon - View Card */}
                    {rowData.cards && rowData.cards.length > 0 && (
                        <>
                            <Tooltip target={`.eye-button-${rowData.id}`} showDelay={100} className="fs-8" />
                            <button
                                className={`btn btn-sm btn-icon eye-button-${rowData.id} me-5 text-blue-500 hover:text-blue-700`}
                                data-pr-position="bottom"
                                data-pr-tooltip="View Card"
                                onClick={() => viewMemberCard(rowData)}>
                                <FaRegEye size={20} />
                            </button>
                        </>
                    )}

                    {/* Edit */}
                    <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                    <button
                        className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
                        data-pr-position="bottom"
                        data-pr-tooltip="Update"
                        onClick={() => editMember(rowData)}>
                        <FiEdit />
                    </button>


                    {/* Delete */}
                    {rowData.id.toString() !== currentUserId && (
                        <>
                            <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                            <button
                                className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
                                data-pr-position="bottom"
                                data-pr-tooltip="Delete"
                                onClick={() => handleDeleteClick(rowData)} >
                                <TfiTrash />
                            </button>
                        </>
                    )}
                </div>
            </>
        );
    }

    const selectAllHandler = (rowsData) => {
        setSelectedMembers((prev) => ({
            ...prev,
            data: rowsData,
            groupData: prev.groupData || {},
            action: !isEmpty(rowsData) ? "update" : "create"
        }));
    }

    const imageBodyTemplate = (rowData) => {
        if (rowData?.tempate_image_html) {
            return <div className="border border-black rounded-md ">{parse(rowData?.tempate_image_html)}</div>
        }
        return;
    }

    const profileBodyTemplate = (rowData) => {
        return rowData?.image ?
            <img loading="lazy" src={rowData?.image} width={100} alt="profile" />
            :
            "";
    }

    const statusBodyTemplate = (rowData) => {
        const status = rowData?.print_status || "unprinted";
        return (
            <span className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles?.[status]}`}>
                {status}
            </span>
        )
    }

    const render3DCard = () => {
        if (!selectedCard) return null;

        return (
            <div className="flex flex-col items-center justify-center p-6">
                <motion.div
                    className="relative"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    onClick={() => setIsFlipped(!isFlipped)}
                >
                    <motion.div
                        className="relative cursor-pointer"
                        style={{
                            transformStyle: 'preserve-3d',
                            perspective: '1000px',
                            width: 'fit-content',
                        }}
                        animate={{
                            rotateY: isFlipped ? 180 : 0,
                        }}
                        transition={{
                            duration: 0.6,
                            ease: "easeInOut",
                        }}
                        whileHover={{ scale: 1.02 }}
                    >
                        {/* Card Front */}
                        <motion.div
                            className="flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
                            style={{
                                backfaceVisibility: 'hidden',
                                background: 'linear-gradient(145deg, #1a2a3a, #2a3a4a)',
                                border: '2px solid rgba(255, 255, 255, 0.1)',
                                width: 'fit-content',
                            }}
                        >
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>

                            <div className="absolute inset-0 rounded-xl overflow-hidden">
                                <div className="absolute -top-10 -left-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
                                <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
                            </div>

                            <div className="relative p-4 flex flex-col items-center max-w-sm mx-auto">
                                <div className="relative overflow-hidden" style={{ maxWidth: '100%' }}>
                                    {selectedCard.image_path ? (
                                        <motion.img
                                            src={selectedCard.image_path}
                                            alt="Card"
                                            className="object-contain"
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            transition={{ delay: 0.3 }}
                                            whileHover={{ scale: 1.05 }}
                                        />
                                    ) : (
                                        <div className="w-64 h-64 bg-gray-700 flex items-center justify-center">
                                            <span className="text-gray-300">No Image</span>
                                        </div>
                                    )}
                                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white opacity-10"></div>

                                    <div className="absolute inset-0 overflow-hidden">
                                        {[...Array(10)].map((_, i) => (
                                            <motion.div
                                                key={i}
                                                className="absolute bg-white rounded-full"
                                                style={{
                                                    width: `${Math.random() * 3 + 1}px`,
                                                    height: `${Math.random() * 3 + 1}px`,
                                                    left: `${Math.random() * 100}%`,
                                                    top: `${Math.random() * 100}%`,
                                                    opacity: Math.random() * 0.3,
                                                }}
                                                animate={{
                                                    y: [0, (Math.random() - 0.5) * 20],
                                                    x: [0, (Math.random() - 0.5) * 20],
                                                }}
                                                transition={{
                                                    duration: Math.random() * 5 + 3,
                                                    repeat: Infinity,
                                                    repeatType: "reverse",
                                                }}
                                            />
                                        ))}
                                    </div>
                                </div>

                                <motion.div
                                    className="absolute inset-0 bg-white opacity-0 pointer-events-none"
                                    initial={{ opacity: 0 }}
                                    animate={{
                                        opacity: isFlipped ? 0 : [0, 0.1, 0],
                                        x: [-100, 300],
                                    }}
                                    transition={{
                                        duration: 1.5,
                                        repeat: Infinity,
                                        repeatDelay: 3,
                                    }}
                                    style={{
                                        background: 'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%)',
                                    }}
                                />
                            </div>
                        </motion.div>

                        {/* Card Back */}
                        <motion.div
                            className="absolute top-0 left-0 flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
                            style={{
                                backfaceVisibility: 'hidden',
                                background: 'linear-gradient(145deg, #2a3a4a, #1a2a3a)',
                                border: '2px solid rgba(255, 255, 255, 0.1)',
                                transform: 'rotateY(180deg)',
                                width: '100%',
                                height: '100%',
                                minWidth: '300px',
                                minHeight: '400px',
                            }}
                        >
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>

                            <div className="absolute inset-0 rounded-xl overflow-hidden">
                                <div className="absolute -top-10 -left-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
                                <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
                            </div>

                            <div className="relative w-full h-full p-6 flex flex-col justify-between">
                                <motion.div
                                    className="w-full h-8 bg-gradient-to-r from-black to-gray-800 rounded-sm mb-4"
                                    initial={{ opacity: 0, y: -20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.2 }}
                                />

                                <motion.div
                                    className="w-full h-16 bg-white bg-opacity-10 rounded-sm flex items-center justify-center mb-4"
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    <div className="w-full h-12 bg-gray-800 rounded-sm flex items-center justify-center">
                                        <span className="text-xs text-gray-400">card informations</span>
                                    </div>
                                </motion.div>

                                <motion.div
                                    className="w-full space-y-3"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.4 }}
                                >
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Member Name:</span>
                                        <span className="text-xs text-white">{selectedCard.member_name || 'N/A'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Member Type:</span>
                                        <span className="text-xs text-white">{selectedCard.member_type || 'N/A'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Serial No:</span>
                                        <span className="text-xs text-white">{selectedCard.number || 'N/A'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Number of colors:</span>
                                        <span className="text-xs text-white">{selectedCard.card_type?.number_of_colors ?? 'N/A'}</span>
                                    </div>
                                </motion.div>

                                <motion.div
                                    className="mt-4 pt-2 border-t border-gray-700"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 }}
                                >
                                    <div className="flex justify-between items-center">
                                        <div>
                                            <p className="text-xs text-blue-200 mb-1">Type Of Connection</p>
                                            <div className="h-8 w-24 bg-gray-700 rounded-sm"></div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-xs text-blue-200">Connection</p>
                                            <p className="text-xs text-white flex items-center justify-end">
                                                {selectedCard?.card_type?.type_of_connection === 'NFC' && (
                                                    <svg className="w-5 h-5 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                    </svg>
                                                )}
                                                {selectedCard?.card_type?.type_of_connection === 'Bluetooth' && (
                                                    <svg className="w-5 h-5 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                    </svg>
                                                )}
                                                {selectedCard?.card_type?.type_of_connection || 'N/A'}
                                            </p>
                                        </div>
                                    </div>
                                </motion.div>

                                <motion.div
                                    className="w-full text-center mt-2"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.6 }}
                                >
                                    <p className="text-[8px] text-blue-300 opacity-70">
                                        This badge is property of INLNULL SYSTEM. If found, please return to nearest office.
                                    </p>
                                </motion.div>
                            </div>
                        </motion.div>
                    </motion.div>
                </motion.div>

                <div className="mt-28 text-center space-y-4">
                    {/* Card Navigation Controls */}
                    {allCards.length > 1 && (
                        <div className="flex items-center justify-center space-x-4 mb-4">
                            <motion.button
                                onClick={goToPreviousCard}
                                disabled={currentCardIndex === 0}
                                className={`p-2 rounded-full ${
                                    currentCardIndex === 0
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90'
                                } transition shadow-md`}
                                whileHover={currentCardIndex !== 0 ? { scale: 1.05 } : {}}
                                whileTap={currentCardIndex !== 0 ? { scale: 0.95 } : {}}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                            </motion.button>

                            {/* Card Indicators */}
                            <div className="flex space-x-2">
                                {allCards.map((_, index) => (
                                    <motion.button
                                        key={index}
                                        onClick={() => goToCard(index)}
                                        className={`w-3 h-3 rounded-full transition ${
                                            index === currentCardIndex
                                                ? 'bg-gradient-to-r from-blue-600 to-purple-600'
                                                : 'bg-gray-300 hover:bg-gray-400'
                                        }`}
                                        whileHover={{ scale: 1.2 }}
                                        whileTap={{ scale: 0.9 }}
                                    />
                                ))}
                            </div>

                            <motion.button
                                onClick={goToNextCard}
                                disabled={currentCardIndex === allCards.length - 1}
                                className={`p-2 rounded-full ${
                                    currentCardIndex === allCards.length - 1
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90'
                                } transition shadow-md`}
                                whileHover={currentCardIndex !== allCards.length - 1 ? { scale: 1.05 } : {}}
                                whileTap={currentCardIndex !== allCards.length - 1 ? { scale: 0.95 } : {}}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </motion.button>
                        </div>
                    )}

                    {/* Card Counter */}
                    {allCards.length > 1 && (
                        <motion.p
                            className="text-sm text-gray-600 font-medium"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.3 }}
                        >
                            Card {currentCardIndex + 1} of {allCards.length}
                        </motion.p>
                    )}

                    <motion.p
                        className="text-sm text-gray-500"
                        animate={{
                            opacity: [0.6, 1, 0.6],
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                        }}
                    >
                        Click to flip
                    </motion.p>

                    {/* Keyboard navigation hint */}
                    {allCards.length > 1 && (
                        <motion.p
                            className="text-xs text-gray-400 font-bold"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                        >
                            Tip : Use (←) (→) arrow keys on your keyboard to navigate
                        </motion.p>
                    )}
                    <div className="flex space-x-4 justify-center">
                        <motion.button
                            onClick={() => setIsFlipped(!isFlipped)}
                            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:opacity-90 transition shadow-md"
                            whileHover={{ scale: 1.05, boxShadow: "0px 0px 15px rgba(99, 102, 241, 0.5)" }}
                            whileTap={{ scale: 0.95 }}
                        >
                            Flip Card
                        </motion.button>
                    </div>
                </div>
            </div>
        );
    };




    const Header = () => {
        // Removed unused variables
        return (
            <div className="w-full flex justify-between">
                {/* <TemplateImageHeader /> */}
                <div className="flex items-center space-x-2">

                <button
                    onClick={handleExport}
                    className="main-btn text-md shadow-md mr-[20px] flex items-center"
                >
                    <FaFileExport className="mr-2" />
                    <span>Export Excel</span>
                </button>


                <button
                    onClick={() => setImportDialogVisible(true)}
                    className="main-btn text-md shadow-md mr-[20px] flex items-center"
                >
                    <FaFileImport className="mr-2" />
                    <span>Import Excel</span>
                </button>

                </div>
            </div>
        )
    }


    return (
        <div className="w-full min-w-full h-full flex flex-col">

        <Toast ref={toast} position="top-right" />

        <ConfirmDialog                                       //This is a small confirm modal for the delete button
            group="headless"
            content={(options) => (
                <div className="flex flex-col items-center p-5">
                    <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
                    <span className="text-xl font-bold mb-2">{options.message}</span>
                    <div className="flex gap-3">
                        <button className="p-button p-component" onClick={options.accept}>
                            Yes
                        </button>
                        <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                            No
                        </button>
                    </div>
                </div>
            )}
        />



            {/*Buttons & Search Bar Container*/}
            <div className="flex justify-between items-start w-full mb-4 gap-4 mt-1">
                <div className="flex items-center">
                    <button className="main-btn text-md shadow-md mr-[20px]" onClick={() => createMember()}>
                        Add Member
                    </button>

                    <button
                        className={`${activeBtn ? "gray-btn" : "main-btn"} text-md me-2 shadow-md`}
                        disabled={activeBtn}
                        onClick={() => createGroup()}
                    >
                        Create Group
                    </button>

                </div>

                {/* Search input */}
                <div className="flex-grow max-w-[700px] relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search by name..."
                        className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                                focus:outline-none focus:ring-2 focus:ring-blue-300
                                focus:border-blue-300 transition-all duration-200"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>

                <div className="flex items-center space-x-2">

                <button
                    onClick={handleExport}
                    className="main-btn text-md shadow-md mr-[20px] flex items-center"
                >
                    <FaFileExport className="mr-2" />
                    <span>Export Excel</span>
                </button>


                <button
                    onClick={() => setImportDialogVisible(true)}
                    className="main-btn text-md shadow-md mr-[20px] flex items-center"
                >
                    <FaFileImport className="mr-2" />
                    <span>Import Excel</span>
                </button>

                </div>

            </div>



            <div className="flex-grow h-full">
                <DataTable
                    className="table border w-full h-full"
                    // scrollHeight="flex"
                    selection={selectedMembers.data}
                    onSelectionChange={(e) => selectAllHandler(e.value)}
                    lazy
                    // filterDisplay="row"
                    // header={Header}
                    responsiveLayout="stack"
                    breakpoint="960px"
                    dataKey="id"
                    paginator
                    value={data}
                    first={lazyParams?.first}
                    rows={lazyParams?.rows}
                    rowsPerPageOptions={[5, 25, 50, 100]}
                    totalRecords={totalRecords}
                    onPage={(e) => {
                        // Only update if actually changed
                        if (e.first !== lazyParams.first || e.rows !== lazyParams.rows) {
                            dataHandler({
                                first: e.first,
                                rows: e.rows,
                                page: Math.floor(e.first / e.rows)
                            });
                        }
                    }}
                    onSort={(e) => {
                        // Only update if actually changed
                        if (e.sortField !== lazyParams.sortField || e.sortOrder !== lazyParams.sortOrder) {
                            dataHandler({
                                sortField: e.sortField,
                                sortOrder: e.sortOrder
                            });
                        }
                    }}
                    onRowsPerPageChange={(e) => {
                        // Only update if actually changed
                        if (e.rows !== lazyParams.rows) {
                            dataHandler({
                                rows: e.rows,
                                first: 0,
                                page: 0
                            });
                        }
                    }}
                    sortField={lazyParams?.sortField}
                    sortOrder={lazyParams?.sortOrder}
                    onFilter={(e) => {
                        // Only update if filters actually changed
                        dataHandler({
                            filters: e.filters
                        });
                    }}
                    filters={lazyParams?.filters}
                    loading={loading}
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                    scrollable
                    scrollHeight="100%"
                >
                    <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} exportable={false} />
                    {/* <Column body={profileBodyTemplate} header="Profile Image" className="text-center" /> */}
                    {/* <Column body={imageBodyTemplate} header="Template Image" className="text-center" /> */}
                    <Column
                        field="name"
                        header="Name"
                        className="text-left"
                        sortable
                        body={(rowData) => (
                            <div className="flex items-center gap-2">
                                <img
                                    src={rowData.image || profile_img} // Adjust this key to match your data
                                    // alt="Profile"
                                    className="w-8 h-8 rounded-full object-cover"
                                />
                                <span>{rowData.name}</span>
                            </div>
                        )}
                    />
                    <Column field="type" header="Type" className="text-center" filter sortable showFilterMenu={false} />
                    <Column field="position" header="Position" className="text-center" filter sortable showFilterMenu={false} />
                    <Column
                        field="cards"
                        header="Associated Card(s)"
                        body={(rowData) => {
                            if (!rowData.cards || rowData.cards.length === 0) {
                                return 'No Card';
                            } else if (rowData.cards.length === 1) {
                                return rowData.cards[0]?.number;
                            } else {
                                return (
                                    <span>
                                        <span className="text-blue-600 font-semibold">{rowData.cards.length}</span> Cards
                                    </span>
                                );
                            }
                        }}
                    />
                    <Column field="department" header="Department" className="text-center" filter sortable showFilterMenu={false} />
                    <Column body={actionBodyTemplate} header="Action" className="text-center w-96" exportable={false} style={{ minWidth: '8rem' }} />

                    {userType === 'admin' && (
                        <Column field="status" body={statusBodyTemplate} header="Package Status" className="text-center" showFilterMenu={false} filter sortable />
                    )}
                </DataTable>
            </div>

            {/* Dialogs */}

            <ImportDialog
                visible={importDialogVisible}
                onHide={() => setImportDialogVisible(false)}
                onImportSuccess={handleImportSuccess}
            />
            {openDialog?.addMember && <AddMemberDialog data={selectedMember} actionType={actionType} />}
            {openDialog?.updateGroup && <AssignGroupDialog />}

            {/* Create Group Modal */}
            {isCreateGroupModalOpen && (
                <GroupForm
                    isModalOpen={isCreateGroupModalOpen}
                    setIsModalOpen={setIsCreateGroupModalOpen}
                    onSuccess={handleGroupSuccess}
                    toast={toast}
                    preselectedMembers={selectedMembers.data}
                />
            )}

            {/* Card View Dialog */}
            <Dialog
                header={allCards.length > 1 ? `Card Preview (${allCards.length} cards)` : "Card Preview"}
                visible={isCardViewOpen}
                style={{ width: '50vw', maxWidth: '800px' }}
                onHide={() => {
                    setIsCardViewOpen(false);
                    setIsFlipped(false);
                    setCurrentCardIndex(0);
                    setAllCards([]);
                }}
                className="badge-view-dialog"
            >
                {render3DCard()}
            </Dialog>
        </div>
    );
}

export default MemberDataTable;